syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";
import "google/protobuf/wrappers.proto";

// 日历查询请求
message CalendarReq {
  int32 year = 1;                           // 公历年份
  int32 month = 2;                          // 公历月份
  string method_code = 3;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 4;                // 日期校正：-3到+3天的偏移量
}

// 批量日历查询请求
message BatchCalendarReq {
  repeated string year_months = 1;          // 年月列表，格式："YYYY-MM"，如："2025-05"
  string method_code = 2;                   // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 3;                // 日期校正：-3到+3天的偏移量
}

// 日历日期信息
message CalendarDateInfo {
  int32 gregorian_year = 1;                 // 公历年
  int32 gregorian_month = 2;                // 公历月
  int32 gregorian_day = 3;                  // 公历日
  int32 hijriah_year = 4;                   // Hijriah年
  int32 hijriah_month = 5;                  // Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)
  int32 hijriah_day = 6;                    // Hijriah日
  string method_code = 7;                   // 计算方法代码
  int32 weekday = 8;                        // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
  int32 pasaran = 9;                        // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
  string weekday_name = 10;                 // 星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)
  string pasaran_name = 11;                 // Pasaran名称（本地化）
  repeated CalendarEventInfo events = 12;   // 当日事件列表
}

// 日历事件信息
message CalendarEventInfo {
  int64 id = 1;                             // 事件ID
  string event_type = 2;                    // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
  string title = 3;                         // 事件标题
  string description = 4;                   // 事件描述
  string jump_url = 5;                      // 点击跳转链接
}

// 日历数据
message CalendarData {
  repeated CalendarDateInfo list = 1;       // 日历数据列表
}

// 日历响应
message CalendarRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CalendarData data = 4;
}

// 批量日历响应
message BatchCalendarRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  BatchCalendarData data = 4;
}

// 批量日历数据
message BatchCalendarData {
  map<string, CalendarData> calendars = 1;  // 日历数据映射，key为"YYYY-MM"格式，value为对应月份的日历数据
}

// 祷告时间查询请求
message GetDailyPrayerTimeReq {
  string date = 1;                        // 日期 YYYY-MM-DD 格式
  double latitude = 2;                    // 纬度
  double longitude = 3;                   // 经度
  string timezone = 4;                    // 时区，如 "Asia/Shanghai"
  string method_code = 5;                 // 计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)
  int32 date_adjustment = 6;              // 日期校正：-3到+3天的偏移量 (这个设置是在日历那边)
}

// 祷告时间查询响应
message GetDailyPrayerTimeRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  PrayerTimeData data = 4;
}

// 每日祷告时间数据
message PrayerTimeData {
  string date = 1;                        // 日期 YYYY-MM-DD 格式
  PrayerTime prayer_time = 2;             // 祷告时间
  IslamicDate islamic_date = 3;           // 伊斯兰历日期
}

// 祷告时间
message PrayerTime {
  string imsak = 1;                       // 伊姆萨克时间（仅斋月期间）
  string subuh = 2;                       // 晨祷时间
  string terbit = 3;                      // 日出时间
  string dhuha = 4;                       // 上午祷告时间（可选，目前还不准确）
  string zuhur = 5;                       // 晌祷时间
  string ashar = 6;                       // 晡祷时间
  string maghrib = 7;                     // 昏祷时间
  string isya = 8;                        // 宵祷时间
}

// 伊斯兰历日期
message IslamicDate {
  int32 year = 1;                         // 伊斯兰历年份
  int32 month = 2;                        // 伊斯兰历月份
  int32 day = 3;                          // 伊斯兰历日期
}

// 获取月度祷告时间请求
message GetMonthlyPrayerTimesReq {
  int32 year = 1;                         // 年份 YYYY 格式
  int32 month = 2;                        // 月份 1-12
  double latitude = 3;                    // 纬度
  double longitude = 4;                   // 经度
  string timezone = 5;                    // 时区，如 "Asia/Shanghai"
  string method_code = 6;                 // 计算方法：AUTO, LFNU, UMMUL_QURA
  int32 date_adjustment = 7;              // 日期校正：-3到+3天的偏移量
}

// 获取月度祷告时间响应
message GetMonthlyPrayerTimesRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  MonthlyPrayerTimesData data = 4;
}

// 月度祷告时间数据
message MonthlyPrayerTimesData {
  repeated PrayerTimeData list = 1;  // 每日祷告时间列表
}

// ==================== 朝觐日程表相关定义 ====================

// 朝觐日程表查询请求
message GetHajjScheduleReq {
  int32 year = 1;                           // 朝觐年份（公历）
  string language = 2;                      // 语言代码：zh-CN, en-US, id-ID
}

// 朝觐日程表响应
message GetHajjScheduleRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajjScheduleData data = 4;
}

// 朝觐日程表数据
message HajjScheduleData {
  int32 year = 1;                           // 朝觐年份
  string description = 2;                   // 日程说明
  repeated HajjScheduleItem schedule_list = 3; // 日程列表
}

// 朝觐日程项目
message HajjScheduleItem {
  int64 id = 1;                             // 日程ID
  string gregorian_date = 2;                // 公历日期 YYYY-MM-DD
  string hijriah_date = 3;                  // 回历日期显示文本，如 "3 Dzulqa'dah 1446 H"
  string event_summary = 4;                 // 事件简述
  string location = 5;                      // 地点
  string additional_info = 6;               // 附加信息
}

// 朝觐日程详情查询请求
message GetHajjScheduleDetailReq {
  int64 schedule_id = 1;                    // 日程ID
  string language = 2;                      // 语言代码：zh-CN, en-US, id-ID
}

// 朝觐日程详情响应
message GetHajjScheduleDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  HajjScheduleDetailData data = 4;
}

// 朝觐日程详情数据
message HajjScheduleDetailData {
  int64 id = 1;                             // 日程ID
  string gregorian_date = 2;                // 公历日期 YYYY-MM-DD
  string hijriah_date = 3;                  // 回历日期显示文本
  string event_summary = 4;                 // 事件简述
  string location = 5;                      // 地点
  string additional_info = 6;               // 附加信息
  string article_detail = 7;                // 详细文章内容
}


// 祷告时间服务定义
service PrayerService {
  // 获取日历数据
  rpc GetCalendar(CalendarReq) returns (CalendarRes);

  // 批量获取多个年月的日历数据
  rpc GetBatchCalendar(BatchCalendarReq) returns (BatchCalendarRes);

  // 获取每天祷告时间（前端自己选择用哪个吧）
  rpc GetDailyPrayerTime(GetDailyPrayerTimeReq) returns (GetDailyPrayerTimeRes);

  // 获取月度祷告时间（前端自己选择用哪个吧）
  rpc GetMonthlyPrayerTimes(GetMonthlyPrayerTimesReq) returns (GetMonthlyPrayerTimesRes);
}
