-- 朝觐时间表（<PERSON><PERSON>）

-- 朝觐时间表主表 - 基础信息
CREATE TABLE `haji_jadwal` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `gregorian_year` INT NOT NULL COMMENT '公历年',
    `gregorian_month` INT NOT NULL COMMENT '公历月',
    `gregorian_day` INT NOT NULL COMMENT '公历日',
    `hijriah_date` VARCHAR(50) NOT NULL COMMENT '伊斯兰历日期显示，如：9 Dzulqa\'dah 1446 H',
    `hijriah_year` INT NOT NULL COMMENT '伊斯兰历年份',
    `hijriah_month` INT NOT NULL COMMENT '伊斯兰历月份',
    `hijriah_day` INT NOT NULL COMMENT '伊斯兰历日期',

    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_gregorian_date` (`gregorian_year`, `gregorian_month`, `gregorian_day`) COMMENT '公历日期索引',
    INDEX `idx_hijriah_date` (`hijriah_year`, `hijriah_month`, `hijriah_day`) COMMENT '伊斯兰历日期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表主表';

-- 朝觐时间表多语言内容表
CREATE TABLE `haji_jadwal_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `jadwal_id` BIGINT UNSIGNED NOT NULL COMMENT '朝觐时间表ID，关联haji_jadwal.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `event_summary` VARCHAR(255) NOT NULL COMMENT '事件简述',
    `location` VARCHAR(255) NOT NULL COMMENT '地点名称',
    `additional_info` TEXT COMMENT '附加信息',
    `article_detail` LONGTEXT COMMENT '文章详情（副文本）',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_jadwal_language` (`jadwal_id`, `language_id`) COMMENT '时间表ID和语言唯一索引',
    INDEX `idx_jadwal_id` (`jadwal_id`) COMMENT '时间表ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    FOREIGN KEY (`jadwal_id`) REFERENCES `haji_jadwal` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表多语言内容表';

-- 朝觐日程说明配置表
CREATE TABLE `haji_jadwal_description` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `year` INT NOT NULL COMMENT '朝觐年份',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `description` TEXT NOT NULL COMMENT '日程说明文字',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year_language` (`year`, `language_id`) COMMENT '年份和语言唯一索引',
    INDEX `idx_year` (`year`) COMMENT '年份索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐日程说明配置表';

-- 初始化朝觐时间表主表数据
INSERT INTO `haji_jadwal` (`gregorian_year`, `gregorian_month`, `gregorian_day`, `hijriah_date`, `hijriah_year`, `hijriah_month`, `hijriah_day`) VALUES
(2025, 5, 1, '3 Dzulqa\'dah 1446 H', 1446, 11, 3),
(2025, 5, 2, '4 Dzulqa\'dah 1446 H', 1446, 11, 4),
(2025, 5, 3, '5 Dzulqa\'dah 1446 H', 1446, 11, 5);

-- 初始化多语言内容数据
INSERT INTO `haji_jadwal_content` (`jadwal_id`, `language_id`, `event_summary`, `location`, `additional_info`, `article_detail`) VALUES
-- 第一天 - 中文
(1, 0, '朝觐者进入阿拉法特', '麦加禁寺', '朝觐团队开始进入阿拉法特地区，准备进行朝觐仪式', '阿拉法特是朝觐最重要的仪式之一，朝觐者在此进行驻留（Wukuf）。这是朝觐的核心仪式，所有朝觐者必须在阿拉法特平原上停留，从日落前到日落后。在这里，朝觐者进行祈祷、忏悔和求恕，这被认为是朝觐中最神圣的时刻。'),
-- 第一天 - 英文
(1, 1, 'Pilgrims enter Arafah', 'Masjidil Haram', 'Hajj groups begin entering the Arafah area to prepare for hajj rituals', 'Arafah is one of the most important rituals of Hajj, where pilgrims perform Wukuf (standing). This is the core ritual of Hajj, where all pilgrims must stay on the plain of Arafah from before sunset until after sunset. Here, pilgrims engage in prayer, repentance, and seeking forgiveness, which is considered the most sacred moment of Hajj.'),
-- 第一天 - 印尼语
(1, 2, 'Jamaah haji masuk Arafah', 'Masjidil Haram', 'Rombongan haji mulai memasuki kawasan Arafah untuk persiapan ibadah haji', 'Arafah adalah salah satu rukun haji yang paling penting, di mana jamaah melakukan Wukuf (berdiri). Ini adalah inti dari ibadah haji, di mana semua jamaah harus berada di padang Arafah dari sebelum matahari terbenam hingga setelah matahari terbenam. Di sini, jamaah melakukan doa, taubat, dan memohon ampunan, yang dianggap sebagai momen paling suci dalam ibadah haji.');

-- 初始化日程说明数据
INSERT INTO `haji_jadwal_description` (`year`, `language_id`, `description`) VALUES
(2025, 0, '此时间表遵循印度尼西亚共和国宗教事务部的朝觐旅行计划，使用基于沙特阿拉伯乌姆古拉历法的伊斯兰历。阿拉法特驻留的时间在沙特阿拉伯王国确定十二月新月后确定。'),
(2025, 1, 'This schedule follows the Hajj Travel Plan of the Ministry of Religious Affairs of the Republic of Indonesia, using the Hijri calendar based on the Ummul Qura calendar of Saudi Arabia. The time for wukuf in Arafah is determined after the rukyat hilal for the beginning of Dzulhijjah by the Kingdom of Saudi Arabia.'),
(2025, 2, 'Jadwal ini mengikuti Rencana Perjalanan Haji Kementerian Agama RI dengan penanggalan Hijriah yang mengacu pada Kalender Ummul Qura Arab Saudi. Waktu wukuf di Arafah ditetapkan setelah rukyat hilal awal Dzulhijjah oleh Kerajaan Arab Saudi.');
