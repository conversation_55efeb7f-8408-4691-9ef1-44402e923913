-- 朝觐时间表（<PERSON><PERSON>）

-- 朝觐时间表 - 统一表结构
CREATE TABLE `haji_jadwal` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `year` INT NOT NULL COMMENT '朝觐年份',
    `date` DATE NOT NULL COMMENT '日期（公历）',
    `hijriah_date` VARCHAR(50) NOT NULL COMMENT '伊斯兰历日期显示，如：9 Dzulqa\'dah 1446 H',
    `hijriah_year` INT NOT NULL COMMENT '伊斯兰历年份',
    `hijriah_month` INT NOT NULL COMMENT '伊斯兰历月份',
    `hijriah_day` INT NOT NULL COMMENT '伊斯兰历日期',
    `location` VARCHAR(255) NOT NULL COMMENT '地点，如：Jamaah haji masuk <PERSON>h <PERSON>',
    `activity_description` TEXT COMMENT '活动描述',
    `activity_type` ENUM('PERJALANAN', 'IBADAH', 'ISTIRAHAT', 'MANASIK', 'LAINNYA') NOT NULL DEFAULT 'LAINNYA' COMMENT '活动类型：PERJALANAN-旅程，IBADAH-礼拜，ISTIRAHAT-休息，MANASIK-朝觐仪式，LAINNYA-其他',
    -- 祷告时间字段
    `imsak_time` TIME COMMENT '伊姆萨克时间',
    `subuh_time` TIME COMMENT '晨祷时间',
    `terbit_time` TIME COMMENT '日出时间',
    `dhuha_time` TIME COMMENT '上午祷告时间',
    `zuhur_time` TIME COMMENT '晌祷时间',
    `ashar_time` TIME COMMENT '晡祷时间',
    `maghrib_time` TIME COMMENT '昏祷时间',
    `isya_time` TIME COMMENT '宵祷时间',
    -- 地理位置信息
    `latitude` DECIMAL(10, 8) COMMENT '纬度',
    `longitude` DECIMAL(11, 8) COMMENT '经度',
    `timezone` VARCHAR(50) DEFAULT 'Asia/Riyadh' COMMENT '时区',
    -- 其他信息
    `notes` TEXT COMMENT '备注信息',
    `sort_order` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重，数字越小越靠前',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_year_date` (`year`, `date`) COMMENT '年份和日期索引',
    INDEX `idx_hijriah_date` (`hijriah_year`, `hijriah_month`, `hijriah_day`) COMMENT '伊斯兰历日期索引',
    INDEX `idx_activity_type` (`activity_type`) COMMENT '活动类型索引',
    INDEX `idx_location` (`location`) COMMENT '地点索引',
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    INDEX `idx_status_date` (`status`, `date`) COMMENT '状态和日期组合索引',
    INDEX `idx_coordinates` (`latitude`, `longitude`) COMMENT '坐标索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表';

-- 初始化示例数据
INSERT INTO `haji_jadwal` (`year`, `date`, `hijriah_date`, `hijriah_year`, `hijriah_month`, `hijriah_day`, `location`, `activity_description`, `activity_type`, `subuh_time`, `zuhur_time`, `ashar_time`, `maghrib_time`, `isya_time`, `latitude`, `longitude`, `sort_order`) VALUES
(2025, '2025-05-26', '1 Dzulqa\'dah 1446 H', 1446, 11, 1, 'Masjidil Haram', 'Jamaah haji masuk Arafah Haji', 'MANASIK', '04:18', '12:25', '15:48', '18:30', '19:45', 21.42251000, 39.82619000, 1),
(2025, '2025-05-27', '2 Dzulqa\'dah 1446 H', 1446, 11, 2, 'Arafah', 'Wukuf di Arafah', 'MANASIK', '04:17', '12:25', '15:49', '18:31', '19:46', 21.35459000, 39.98459000, 2),
(2025, '2025-05-28', '3 Dzulqa\'dah 1446 H', 1446, 11, 3, 'Muzdalifah', 'Mabit di Muzdalifah', 'MANASIK', '04:16', '12:25', '15:49', '18:31', '19:46', 21.40367000, 39.93540000, 3),
(2025, '2025-05-29', '4 Dzulqa\'dah 1446 H', 1446, 11, 4, 'Mina', 'Melontar jumrah dan berkurban', 'MANASIK', '04:15', '12:24', '15:50', '18:32', '19:47', 21.41205000, 39.89755000, 4);
