-- 朝觐祷告时间表相关表结构

-- 朝觐祷告时间表主表
CREATE TABLE `jadwal_haji` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `year` INT NOT NULL COMMENT '朝觐年份',
    `title` VARCHAR(255) NOT NULL COMMENT '朝觐标题，如：Jadwal Haji 1446 H',
    `description` TEXT COMMENT '朝觐描述信息',
    `start_date` DATE NOT NULL COMMENT '朝觐开始日期（公历）',
    `end_date` DATE NOT NULL COMMENT '朝觐结束日期（公历）',
    `hijriah_year` INT NOT NULL COMMENT '伊斯兰历年份',
    `hijriah_start_month` INT NOT NULL COMMENT '伊斯兰历开始月份',
    `hijriah_start_day` INT NOT NULL COMMENT '伊斯兰历开始日期',
    `hijriah_end_month` INT NOT NULL COMMENT '伊斯兰历结束月份',
    `hijriah_end_day` INT NOT NULL COMMENT '伊斯兰历结束日期',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `is_current` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否当前朝觐：0-否，1-是',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year` (`year`) COMMENT '年份唯一索引',
    INDEX `idx_hijriah_year` (`hijriah_year`) COMMENT '伊斯兰历年份索引',
    INDEX `idx_status_current` (`status`, `is_current`) COMMENT '状态和当前朝觐索引',
    INDEX `idx_date_range` (`start_date`, `end_date`) COMMENT '日期范围索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐祷告时间表主表';

-- 朝觐祷告时间详情表
CREATE TABLE `jadwal_haji_detail` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `jadwal_haji_id` BIGINT UNSIGNED NOT NULL COMMENT '朝觐主表ID',
    `date` DATE NOT NULL COMMENT '祷告日期（公历）',
    `hijriah_date` VARCHAR(50) NOT NULL COMMENT '伊斯兰历日期显示，如：9 Dzulqa\'dah 1446 H',
    `hijriah_year` INT NOT NULL COMMENT '伊斯兰历年份',
    `hijriah_month` INT NOT NULL COMMENT '伊斯兰历月份',
    `hijriah_day` INT NOT NULL COMMENT '伊斯兰历日期',
    `location` VARCHAR(255) NOT NULL COMMENT '地点，如：Jamaah haji masuk Arafah Haji',
    `activity_type` ENUM('PERJALANAN', 'IBADAH', 'ISTIRAHAT', 'MANASIK') NOT NULL COMMENT '活动类型：PERJALANAN-旅程，IBADAH-礼拜，ISTIRAHAT-休息，MANASIK-朝觐仪式',
    `activity_description` TEXT COMMENT '活动描述',
    `notes` TEXT COMMENT '备注信息',
    `sort_order` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重，数字越小越靠前',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_jadwal_haji_id` (`jadwal_haji_id`) COMMENT '朝觐主表ID索引',
    INDEX `idx_date` (`date`) COMMENT '日期索引',
    INDEX `idx_hijriah_date` (`hijriah_year`, `hijriah_month`, `hijriah_day`) COMMENT '伊斯兰历日期索引',
    INDEX `idx_activity_type` (`activity_type`) COMMENT '活动类型索引',
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    INDEX `idx_status_date` (`status`, `date`) COMMENT '状态和日期组合索引',
    FOREIGN KEY (`jadwal_haji_id`) REFERENCES `jadwal_haji`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐祷告时间详情表';

-- 朝觐祷告时间表
CREATE TABLE `jadwal_prayer_times` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `jadwal_haji_detail_id` BIGINT UNSIGNED NOT NULL COMMENT '朝觐详情表ID',
    `prayer_type` ENUM('IMSAK', 'SUBUH', 'TERBIT', 'DHUHA', 'ZUHUR', 'ASHAR', 'MAGHRIB', 'ISYA') NOT NULL COMMENT '祷告类型',
    `prayer_time` TIME NOT NULL COMMENT '祷告时间',
    `prayer_time_display` VARCHAR(20) NOT NULL COMMENT '祷告时间显示格式，如：05:30',
    `is_main_prayer` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否主要祷告：0-否，1-是（五次主要祷告）',
    `notes` VARCHAR(500) COMMENT '祷告时间备注',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_detail_id` (`jadwal_haji_detail_id`) COMMENT '详情表ID索引',
    INDEX `idx_prayer_type` (`prayer_type`) COMMENT '祷告类型索引',
    INDEX `idx_prayer_time` (`prayer_time`) COMMENT '祷告时间索引',
    INDEX `idx_main_prayer` (`is_main_prayer`) COMMENT '主要祷告索引',
    UNIQUE KEY `uk_detail_prayer` (`jadwal_haji_detail_id`, `prayer_type`) COMMENT '详情ID和祷告类型唯一索引',
    FOREIGN KEY (`jadwal_haji_detail_id`) REFERENCES `jadwal_haji_detail`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐祷告时间表';

-- 朝觐地点信息表
CREATE TABLE `jadwal_locations` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(255) NOT NULL COMMENT '地点名称',
    `name_arabic` VARCHAR(255) COMMENT '阿拉伯语地点名称',
    `name_english` VARCHAR(255) COMMENT '英语地点名称',
    `description` TEXT COMMENT '地点描述',
    `latitude` DECIMAL(10, 8) COMMENT '纬度',
    `longitude` DECIMAL(11, 8) COMMENT '经度',
    `timezone` VARCHAR(50) DEFAULT 'Asia/Riyadh' COMMENT '时区',
    `location_type` ENUM('MASJID', 'HOTEL', 'TRANSPORT', 'HOLY_SITE', 'OTHER') NOT NULL COMMENT '地点类型：MASJID-清真寺，HOTEL-酒店，TRANSPORT-交通，HOLY_SITE-圣地，OTHER-其他',
    `is_active` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_name` (`name`) COMMENT '地点名称索引',
    INDEX `idx_location_type` (`location_type`) COMMENT '地点类型索引',
    INDEX `idx_coordinates` (`latitude`, `longitude`) COMMENT '坐标索引',
    INDEX `idx_active` (`is_active`) COMMENT '启用状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐地点信息表';

-- 朝觐团组信息表
CREATE TABLE `jadwal_groups` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `jadwal_haji_id` BIGINT UNSIGNED NOT NULL COMMENT '朝觐主表ID',
    `group_name` VARCHAR(255) NOT NULL COMMENT '团组名称',
    `group_code` VARCHAR(50) NOT NULL COMMENT '团组代码',
    `leader_name` VARCHAR(255) COMMENT '团长姓名',
    `leader_phone` VARCHAR(50) COMMENT '团长电话',
    `total_members` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '团组总人数',
    `departure_date` DATE COMMENT '出发日期',
    `return_date` DATE COMMENT '返回日期',
    `notes` TEXT COMMENT '团组备注',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_jadwal_haji_id` (`jadwal_haji_id`) COMMENT '朝觐主表ID索引',
    INDEX `idx_group_code` (`group_code`) COMMENT '团组代码索引',
    INDEX `idx_departure_date` (`departure_date`) COMMENT '出发日期索引',
    INDEX `idx_status` (`status`) COMMENT '状态索引',
    FOREIGN KEY (`jadwal_haji_id`) REFERENCES `jadwal_haji`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐团组信息表';

-- 初始化数据示例
INSERT INTO `jadwal_haji` (`year`, `title`, `description`, `start_date`, `end_date`, `hijriah_year`, `hijriah_start_month`, `hijriah_start_day`, `hijriah_end_month`, `hijriah_end_day`, `is_current`) VALUES
(2025, 'Jadwal Haji 1446 H', '2025年朝觐时间安排', '2025-05-26', '2025-06-15', 1446, 11, 1, 12, 20, 1);

-- 初始化地点数据
INSERT INTO `jadwal_locations` (`name`, `name_arabic`, `name_english`, `description`, `latitude`, `longitude`, `location_type`) VALUES
('Masjidil Haram', 'المسجد الحرام', 'The Great Mosque of Mecca', '麦加大清真寺', 21.42251000, 39.82619000, 'MASJID'),
('Arafah', 'عرفة', 'Mount Arafat', '阿拉法特山', 21.35459000, 39.98459000, 'HOLY_SITE'),
('Muzdalifah', 'مزدلفة', 'Muzdalifah', '穆兹达利法', 21.40367000, 39.93540000, 'HOLY_SITE'),
('Mina', 'منى', 'Mina', '米纳', 21.41205000, 39.89755000, 'HOLY_SITE'),
('Masjid Nabawi', 'المسجد النبوي', 'Prophet\'s Mosque', '先知清真寺', 24.46791000, 39.61107000, 'MASJID');
